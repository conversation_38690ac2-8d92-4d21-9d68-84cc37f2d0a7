import { Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { AdditionalServiceOrder } from '../entity/additional-service-order.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Order } from '../entity/order.entity';
import { Employee } from '../entity/employee.entity';
import { Customer } from '../entity/customer.entity';
import { AdditionalServiceOrderDetail } from '../entity/additional-service-order-detail.entity';
import { AdditionalService } from '../entity/additional-service.entity';
import { Op } from 'sequelize';

@Provide()
export class AdditionalServiceDurationStatisticsService extends BaseService<AdditionalServiceOrder> {
  getModel() {
    return AdditionalServiceOrder;
  }

  /**
   * 获取增项服务时长统计
   */
  async getAdditionalServiceDurationStatistics(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
    additionalServiceId?: number;
    orderId?: number;
    page?: number;
    pageSize?: number;
  }) {
    const {
      startDate,
      endDate,
      employeeId,
      additionalServiceId,
      orderId,
      page = 1,
      pageSize = 20,
    } = query;

    // 构建查询条件
    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    const includeConditions: any[] = [
      {
        model: Employee,
        attributes: ['id', 'name', 'phone'],
      },
      {
        model: Customer,
        attributes: ['id', 'nickname', 'phone'],
      },
      {
        model: OrderDetail,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'serviceTime'],
            where: orderId ? { id: orderId } : {},
          },
        ],
      },
      {
        model: AdditionalServiceOrderDetail,
        include: [
          {
            model: AdditionalService,
            attributes: ['id', 'name'],
            where: additionalServiceId ? { id: additionalServiceId } : {},
          },
        ],
      },
    ];

    const { rows: additionalServiceOrders, count } = await AdditionalServiceOrder.findAndCountAll({
      where: whereConditions,
      include: includeConditions,
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['actualServiceEndTime', 'DESC']],
    });

    // 格式化返回数据
    const list = additionalServiceOrders.map(order => ({
      additionalServiceOrderId: order.id,
      additionalServiceOrderSn: order.sn,
      mainOrderId: order.orderDetail?.order?.id,
      mainOrderSn: order.orderDetail?.order?.sn,
      employee: order.employee
        ? {
            id: order.employee.id,
            name: order.employee.name,
            phone: order.employee.phone,
          }
        : null,
      customer: {
        id: order.customer.id,
        nickname: order.customer.nickname,
        phone: order.customer.phone,
      },
      additionalServices: order.details?.map(detail => ({
        additionalServiceId: detail.additionalService?.id,
        additionalServiceName: detail.additionalService?.name,
        quantity: detail.quantity,
      })) || [],
      actualServiceStartTime: order.actualServiceStartTime,
      actualServiceEndTime: order.actualServiceEndTime,
      actualServiceDuration: order.actualServiceDuration,
      totalFee: order.totalFee,
      status: order.status,
    }));

    return {
      list,
      total: count,
      page,
      pageSize,
      totalPages: Math.ceil(count / pageSize),
    };
  }

  /**
   * 获取增项服务时长概览统计
   */
  async getAdditionalServiceDurationOverview(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    const { startDate, endDate, employeeId } = query;

    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    // 统计总体数据
    const totalOrders = await AdditionalServiceOrder.count({
      where: whereConditions,
    });

    const durationStats = await AdditionalServiceOrder.findAll({
      where: whereConditions,
      attributes: [
        [this.sequelize.fn('AVG', this.sequelize.col('actualServiceDuration')), 'avgDuration'],
        [this.sequelize.fn('MIN', this.sequelize.col('actualServiceDuration')), 'minDuration'],
        [this.sequelize.fn('MAX', this.sequelize.col('actualServiceDuration')), 'maxDuration'],
        [this.sequelize.fn('SUM', this.sequelize.col('actualServiceDuration')), 'totalDuration'],
      ],
      raw: true,
    });

    const stats = durationStats[0] as any;

    return {
      totalOrders,
      avgDuration: Math.round(Number(stats.avgDuration) || 0),
      minDuration: Number(stats.minDuration) || 0,
      maxDuration: Number(stats.maxDuration) || 0,
      totalDuration: Number(stats.totalDuration) || 0,
    };
  }

  /**
   * 按增项服务类型统计服务时长
   */
  async getAdditionalServiceDurationByType(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    const { startDate, endDate, employeeId } = query;

    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    const results = await AdditionalServiceOrder.findAll({
      where: whereConditions,
      include: [
        {
          model: AdditionalServiceOrderDetail,
          include: [
            {
              model: AdditionalService,
              attributes: ['id', 'name'],
            },
          ],
        },
      ],
      attributes: ['actualServiceDuration'],
    });

    // 按增项服务类型分组统计
    const serviceStats: { [key: string]: any } = {};

    results.forEach(order => {
      order.details?.forEach(detail => {
        const serviceName = detail.additionalService?.name || '未知增项服务';
        const serviceId = detail.additionalService?.id;

        if (!serviceStats[serviceName]) {
          serviceStats[serviceName] = {
            additionalServiceId: serviceId,
            additionalServiceName: serviceName,
            orderCount: 0,
            totalDuration: 0,
            avgDuration: 0,
          };
        }

        serviceStats[serviceName].orderCount += 1;
        serviceStats[serviceName].totalDuration += order.actualServiceDuration || 0;
      });
    });

    // 计算平均时长
    Object.values(serviceStats).forEach((stat: any) => {
      stat.avgDuration = Math.round(stat.totalDuration / stat.orderCount);
    });

    return Object.values(serviceStats);
  }

  /**
   * 按员工统计增项服务时长
   */
  async getAdditionalServiceDurationByEmployee(query: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }) {
    const { startDate, endDate, page = 1, pageSize = 20 } = query;

    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
      employeeId: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    const results = await AdditionalServiceOrder.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      attributes: ['employeeId', 'actualServiceDuration'],
    });

    // 按员工分组统计
    const employeeStats: { [key: number]: any } = {};

    results.forEach(order => {
      const employeeId = order.employeeId!;

      if (!employeeStats[employeeId]) {
        employeeStats[employeeId] = {
          employeeId,
          employeeName: order.employee?.name || '未知员工',
          employeePhone: order.employee?.phone,
          orderCount: 0,
          totalDuration: 0,
          avgDuration: 0,
        };
      }

      employeeStats[employeeId].orderCount += 1;
      employeeStats[employeeId].totalDuration += order.actualServiceDuration || 0;
    });

    // 计算平均时长并排序
    const list = Object.values(employeeStats)
      .map((stat: any) => ({
        ...stat,
        avgDuration: Math.round(stat.totalDuration / stat.orderCount),
      }))
      .sort((a, b) => b.totalDuration - a.totalDuration);

    const total = list.length;
    const paginatedList = list.slice((page - 1) * pageSize, page * pageSize);

    return {
      list: paginatedList,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }
}
