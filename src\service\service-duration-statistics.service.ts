import { Provide, Inject } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { ServiceChangeLog } from '../entity/service-change-log.entity';
import { Order } from '../entity/order.entity';
import { Employee } from '../entity/employee.entity';
import { Customer } from '../entity/customer.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Service } from '../entity/service.entity';
import { Op, Sequelize } from 'sequelize';

@Provide()
export class ServiceDurationStatisticsService extends BaseService<ServiceChangeLog> {
  @Inject()
  sequelize: Sequelize;

  getModel() {
    return ServiceChangeLog;
  }

  /**
   * 获取服务时长统计
   */
  async getServiceDurationStatistics(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
    serviceId?: number;
    orderId?: number;
    page?: number;
    pageSize?: number;
  }) {
    const {
      startDate,
      endDate,
      employeeId,
      serviceId,
      orderId,
      page = 1,
      pageSize = 20,
    } = query;

    // 构建查询条件
    const whereConditions: any = {};
    const orderWhereConditions: any = {};

    if (startDate || endDate) {
      orderWhereConditions.createdAt = {};
      if (startDate) {
        orderWhereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        orderWhereConditions.createdAt[Op.lte] = new Date(
          endDate + ' 23:59:59'
        );
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    if (orderId) {
      whereConditions.orderId = orderId;
    }

    // 查询所有已完成的订单及其服务时长信息
    const { rows: orders, count } = await Order.findAndCountAll({
      where: {
        ...orderWhereConditions,
        actualServiceStartTime: { [Op.not]: null },
        actualServiceEndTime: { [Op.not]: null },
      },
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              attributes: ['id', 'serviceName'],
              where: serviceId ? { id: serviceId } : {},
            },
          ],
        },
        {
          model: ServiceChangeLog,
          where: whereConditions,
          required: false,
          attributes: ['changeType', 'createdAt', 'description'],
        },
      ],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['actualServiceEndTime', 'DESC']],
    });

    // 格式化返回数据
    const list = orders.map(order => ({
      orderId: order.id,
      orderSn: order.sn,
      employee: order.employee
        ? {
            id: order.employee.id,
            name: order.employee.name,
            phone: order.employee.phone,
          }
        : null,
      customer: {
        id: order.customer.id,
        nickname: order.customer.nickname,
        phone: order.customer.phone,
      },
      services:
        order.orderDetails?.map(detail => ({
          serviceId: detail.service?.id,
          serviceName: detail.service?.serviceName,
          quantity: 1, // 每个订单明细代表一个服务项目
        })) || [],
      serviceTime: order.serviceTime,
      actualServiceStartTime: order.actualServiceStartTime,
      actualServiceEndTime: order.actualServiceEndTime,
      actualServiceDuration: order.actualServiceDuration,
      totalFee: order.totalFee,
      changeLogs: order.changeLogs || [],
    }));

    return {
      list,
      total: count,
      page,
      pageSize,
      totalPages: Math.ceil(count / pageSize),
    };
  }

  /**
   * 获取服务时长概览统计
   */
  async getServiceDurationOverview(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    const { startDate, endDate, employeeId } = query;

    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    // 统计总体数据
    const totalOrders = await Order.count({
      where: whereConditions,
    });

    const durationStats = await Order.findAll({
      where: whereConditions,
      attributes: [
        [
          this.sequelize.fn('AVG', this.sequelize.col('actualServiceDuration')),
          'avgDuration',
        ],
        [
          this.sequelize.fn('MIN', this.sequelize.col('actualServiceDuration')),
          'minDuration',
        ],
        [
          this.sequelize.fn('MAX', this.sequelize.col('actualServiceDuration')),
          'maxDuration',
        ],
        [
          this.sequelize.fn('SUM', this.sequelize.col('actualServiceDuration')),
          'totalDuration',
        ],
      ],
      raw: true,
    });

    const stats = durationStats[0] as any;

    return {
      totalOrders,
      avgDuration: Math.round(Number(stats.avgDuration) || 0),
      minDuration: Number(stats.minDuration) || 0,
      maxDuration: Number(stats.maxDuration) || 0,
      totalDuration: Number(stats.totalDuration) || 0,
    };
  }

  /**
   * 按服务类型统计服务时长
   */
  async getServiceDurationByServiceType(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    const { startDate, endDate, employeeId } = query;

    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    const results = await Order.findAll({
      where: whereConditions,
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              attributes: ['id', 'serviceName'],
            },
          ],
        },
      ],
      attributes: ['actualServiceDuration'],
    });

    // 按服务类型分组统计
    const serviceStats: { [key: string]: any } = {};

    results.forEach(order => {
      order.orderDetails?.forEach(detail => {
        const serviceName = detail.service?.serviceName || '未知服务';
        const serviceId = detail.service?.id;

        if (!serviceStats[serviceName]) {
          serviceStats[serviceName] = {
            serviceId,
            serviceName,
            orderCount: 0,
            totalDuration: 0,
            avgDuration: 0,
          };
        }

        serviceStats[serviceName].orderCount += 1;
        serviceStats[serviceName].totalDuration +=
          order.actualServiceDuration || 0;
      });
    });

    // 计算平均时长
    Object.values(serviceStats).forEach((stat: any) => {
      stat.avgDuration = Math.round(stat.totalDuration / stat.orderCount);
    });

    return Object.values(serviceStats);
  }

  /**
   * 按员工统计服务时长
   */
  async getServiceDurationByEmployee(query: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }) {
    const { startDate, endDate, page = 1, pageSize = 20 } = query;

    const whereConditions: any = {
      actualServiceStartTime: { [Op.not]: null },
      actualServiceEndTime: { [Op.not]: null },
      employeeId: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    const results = await Order.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      attributes: ['employeeId', 'actualServiceDuration'],
    });

    // 按员工分组统计
    const employeeStats: { [key: number]: any } = {};

    results.forEach(order => {
      const employeeId = order.employeeId!;

      if (!employeeStats[employeeId]) {
        employeeStats[employeeId] = {
          employeeId,
          employeeName: order.employee?.name || '未知员工',
          employeePhone: order.employee?.phone,
          orderCount: 0,
          totalDuration: 0,
          avgDuration: 0,
        };
      }

      employeeStats[employeeId].orderCount += 1;
      employeeStats[employeeId].totalDuration +=
        order.actualServiceDuration || 0;
    });

    // 计算平均时长并排序
    const list = Object.values(employeeStats)
      .map((stat: any) => ({
        ...stat,
        avgDuration: Math.round(stat.totalDuration / stat.orderCount),
      }))
      .sort((a, b) => b.totalDuration - a.totalDuration);

    const total = list.length;
    const paginatedList = list.slice((page - 1) * pageSize, page * pageSize);

    return {
      list: paginatedList,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }
}
