import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AdditionalServiceDurationStatisticsService } from '../service/additional-service-duration-statistics.service';

@Controller('/additional-service-duration-statistics')
export class AdditionalServiceDurationStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  additionalServiceDurationStatisticsService: AdditionalServiceDurationStatisticsService;

  /**
   * 获取增项服务时长统计列表
   */
  @Get('/list')
  async getAdditionalServiceDurationList(@Query() query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
    additionalServiceId?: number;
    orderId?: number;
    page?: number;
    pageSize?: number;
  }) {
    return await this.additionalServiceDurationStatisticsService.getAdditionalServiceDurationStatistics(query);
  }

  /**
   * 获取增项服务时长概览统计
   */
  @Get('/overview')
  async getAdditionalServiceDurationOverview(@Query() query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    return await this.additionalServiceDurationStatisticsService.getAdditionalServiceDurationOverview(query);
  }

  /**
   * 按增项服务类型统计服务时长
   */
  @Get('/by-type')
  async getAdditionalServiceDurationByType(@Query() query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    return await this.additionalServiceDurationStatisticsService.getAdditionalServiceDurationByType(query);
  }

  /**
   * 按员工统计增项服务时长
   */
  @Get('/by-employee')
  async getAdditionalServiceDurationByEmployee(@Query() query: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }) {
    return await this.additionalServiceDurationStatisticsService.getAdditionalServiceDurationByEmployee(query);
  }
}
