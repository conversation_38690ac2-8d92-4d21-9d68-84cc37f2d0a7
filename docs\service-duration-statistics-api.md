# 服务时长统计接口文档

## 概述
本文档描述了主订单服务时长统计相关的API接口，基于订单状态变更记录计算实际服务时长。

## 管理端接口

### 1. 获取服务时长统计列表

**接口地址：** `GET /service-duration-statistics/list`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1,                     // 可选，员工ID
  "serviceId": 1,                      // 可选，服务ID
  "orderId": 1,                        // 可选，订单ID
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "orderId": 1,
        "orderSn": "20240101001",
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001"
        },
        "customer": {
          "id": 1,
          "nickname": "小明",
          "phone": "13800138002"
        },
        "services": [
          {
            "serviceId": 1,
            "serviceName": "宠物洗护",
            "quantity": 1
          }
        ],
        "serviceTime": "2024-01-01T10:00:00.000Z",
        "actualServiceStartTime": "2024-01-01T10:30:00.000Z",
        "actualServiceEndTime": "2024-01-01T11:30:00.000Z",
        "actualServiceDuration": 60,
        "totalFee": 100.00,
        "changeLogs": []
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

### 2. 获取服务时长概览统计

**接口地址：** `GET /service-duration-statistics/overview`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "totalOrders": 100,                // 总订单数
    "avgDuration": 75,                 // 平均服务时长（分钟）
    "minDuration": 30,                 // 最短服务时长（分钟）
    "maxDuration": 120,                // 最长服务时长（分钟）
    "totalDuration": 7500              // 总服务时长（分钟）
  }
}
```

### 3. 按服务类型统计服务时长

**接口地址：** `GET /service-duration-statistics/by-service-type`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": [
    {
      "serviceId": 1,
      "serviceName": "宠物洗护",
      "orderCount": 50,
      "totalDuration": 3750,
      "avgDuration": 75
    },
    {
      "serviceId": 2,
      "serviceName": "宠物美容",
      "orderCount": 30,
      "totalDuration": 3600,
      "avgDuration": 120
    }
  ]
}
```

### 4. 按员工统计服务时长

**接口地址：** `GET /service-duration-statistics/by-employee`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "employeeId": 1,
        "employeeName": "张师傅",
        "employeePhone": "13800138001",
        "orderCount": 50,
        "totalDuration": 3750,
        "avgDuration": 75
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

### 5. 获取增项服务时长统计列表

**接口地址：** `GET /additional-service-duration-statistics/list`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1,                     // 可选，员工ID
  "additionalServiceId": 1,            // 可选，增项服务ID
  "orderId": 1,                        // 可选，主订单ID
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "additionalServiceOrderId": 1,
        "additionalServiceOrderSn": "AS20240101001",
        "mainOrderId": 1,
        "mainOrderSn": "20240101001",
        "employee": {
          "id": 1,
          "name": "张师傅",
          "phone": "13800138001"
        },
        "customer": {
          "id": 1,
          "nickname": "小明",
          "phone": "13800138002"
        },
        "additionalServices": [
          {
            "additionalServiceId": 1,
            "additionalServiceName": "除虫服务",
            "quantity": 1
          }
        ],
        "actualServiceStartTime": "2024-01-01T10:30:00.000Z",
        "actualServiceEndTime": "2024-01-01T11:00:00.000Z",
        "actualServiceDuration": 30,
        "totalFee": 50.00,
        "status": "completed"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```

### 6. 获取增项服务时长概览统计

**接口地址：** `GET /additional-service-duration-statistics/overview`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "totalOrders": 50,                 // 总增项服务订单数
    "avgDuration": 35,                 // 平均服务时长（分钟）
    "minDuration": 15,                 // 最短服务时长（分钟）
    "maxDuration": 60,                 // 最长服务时长（分钟）
    "totalDuration": 1750              // 总服务时长（分钟）
  }
}
```

### 7. 按增项服务类型统计服务时长

**接口地址：** `GET /additional-service-duration-statistics/by-type`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "employeeId": 1                      // 可选，员工ID
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": [
    {
      "additionalServiceId": 1,
      "additionalServiceName": "除虫服务",
      "orderCount": 30,
      "totalDuration": 900,
      "avgDuration": 30
    },
    {
      "additionalServiceId": 2,
      "additionalServiceName": "指甲修剪",
      "orderCount": 20,
      "totalDuration": 400,
      "avgDuration": 20
    }
  ]
}
```

### 8. 按员工统计增项服务时长

**接口地址：** `GET /additional-service-duration-statistics/by-employee`

**请求参数：**
```json
{
  "startDate": "2024-01-01",           // 可选，开始日期
  "endDate": "2024-01-31",             // 可选，结束日期
  "page": 1,                           // 可选，页码，默认1
  "pageSize": 20                       // 可选，每页数量，默认20
}
```

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "employeeId": 1,
        "employeeName": "张师傅",
        "employeePhone": "13800138001",
        "orderCount": 30,
        "totalDuration": 900,
        "avgDuration": 30
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

## 字段说明

### 时间字段
- `actualServiceStartTime`: 实际服务开始时间，ISO 8601格式
- `actualServiceEndTime`: 实际服务结束时间，ISO 8601格式
- `actualServiceDuration`: 实际服务时长，单位为分钟

### 状态说明
- 主订单服务时长：基于订单状态变更记录中的"开始服务"和"完成订单"时间计算
- 增项服务时长：基于主订单开始服务时同步开始，主订单完成时同步结束

## 注意事项

1. 只有已完成的订单才会有完整的服务时长数据
2. 时长统计基于实际操作时间，不是预约时间
3. 增项服务的时长与主订单服务时长同步
4. 所有时间均为服务器时间（UTC+8）
5. 分页查询默认按服务结束时间倒序排列
