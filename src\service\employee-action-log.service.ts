import { Inject, Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { ServiceChangeLog } from '../entity/service-change-log.entity';
import { Order } from '../entity/order.entity';
import { Employee } from '../entity/employee.entity';
import { Customer } from '../entity/customer.entity';
import { OrderStatusChangeType } from '../common/Constant';
import { Op, Sequelize } from 'sequelize';

@Provide()
export class EmployeeActionLogService extends BaseService<ServiceChangeLog> {
  @Inject()
  sequelize: Sequelize;

  getModel() {
    return ServiceChangeLog;
  }

  /**
   * 获取员工动作记录列表
   */
  async getEmployeeActionLogs(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
    orderId?: number;
    changeType?: string;
    page?: number;
    pageSize?: number;
  }) {
    const {
      startDate,
      endDate,
      employeeId,
      orderId,
      changeType,
      page = 1,
      pageSize = 20,
    } = query;

    // 构建查询条件
    const whereConditions: any = {
      employeeId: { [Op.not]: null }, // 只查询员工操作的记录
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    if (orderId) {
      whereConditions.orderId = orderId;
    }

    if (changeType) {
      whereConditions.changeType = changeType;
    }

    const { rows: logs, count } = await ServiceChangeLog.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'serviceTime', 'totalFee', 'status'],
          include: [
            {
              model: Customer,
              attributes: ['id', 'nickname', 'phone'],
            },
          ],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      order: [['createdAt', 'DESC']],
    });

    // 格式化返回数据
    const list = logs.map(log => ({
      id: log.id,
      orderId: log.orderId,
      orderSn: log.order?.sn,
      orderStatus: log.order?.status,
      serviceTime: log.order?.serviceTime,
      totalFee: log.order?.totalFee,
      customer: log.order?.customer
        ? {
            id: log.order.customer.id,
            nickname: log.order.customer.nickname,
            phone: log.order.customer.phone,
          }
        : null,
      employee: log.employee
        ? {
            id: log.employee.id,
            name: log.employee.name,
            phone: log.employee.phone,
          }
        : null,
      changeType: log.changeType,
      changeTypeLabel: this.getChangeTypeLabel(log.changeType),
      description: log.description,
      actionTime: log.createdAt,
    }));

    return {
      list,
      total: count,
      page,
      pageSize,
      totalPages: Math.ceil(count / pageSize),
    };
  }

  /**
   * 获取员工动作统计概览
   */
  async getEmployeeActionOverview(query: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }) {
    const { startDate, endDate, employeeId } = query;

    const whereConditions: any = {
      employeeId: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (employeeId) {
      whereConditions.employeeId = employeeId;
    }

    // 统计各种动作类型的数量
    const actionStats = await ServiceChangeLog.findAll({
      where: whereConditions,
      attributes: [
        'changeType',
        [this.sequelize.fn('COUNT', this.sequelize.col('id')), 'count'],
      ],
      group: ['changeType'],
      raw: true,
    });

    // 格式化统计数据
    const stats = actionStats.reduce((acc: any, stat: any) => {
      acc[stat.changeType] = {
        changeType: stat.changeType,
        changeTypeLabel: this.getChangeTypeLabel(stat.changeType),
        count: Number(stat.count),
      };
      return acc;
    }, {});

    // 计算总动作数
    const totalActions = actionStats.reduce(
      (sum, stat: any) => sum + Number(stat.count),
      0
    );

    return {
      totalActions,
      actionStats: Object.values(stats),
    };
  }

  /**
   * 按员工统计动作记录
   */
  async getActionStatsByEmployee(query: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  }) {
    const { startDate, endDate, page = 1, pageSize = 20 } = query;

    const whereConditions: any = {
      employeeId: { [Op.not]: null },
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    const results = await ServiceChangeLog.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
      attributes: ['employeeId', 'changeType'],
    });

    // 按员工分组统计
    const employeeStats: { [key: number]: any } = {};

    results.forEach(log => {
      const employeeId = log.employeeId!;

      if (!employeeStats[employeeId]) {
        employeeStats[employeeId] = {
          employeeId,
          employeeName: log.employee?.name || '未知员工',
          employeePhone: log.employee?.phone,
          totalActions: 0,
          actionBreakdown: {},
        };
      }

      employeeStats[employeeId].totalActions += 1;

      const changeType = log.changeType;
      if (!employeeStats[employeeId].actionBreakdown[changeType]) {
        employeeStats[employeeId].actionBreakdown[changeType] = {
          changeType,
          changeTypeLabel: this.getChangeTypeLabel(changeType),
          count: 0,
        };
      }
      employeeStats[employeeId].actionBreakdown[changeType].count += 1;
    });

    // 转换为数组并排序
    const list = Object.values(employeeStats)
      .map((stat: any) => ({
        ...stat,
        actionBreakdown: Object.values(stat.actionBreakdown),
      }))
      .sort((a, b) => b.totalActions - a.totalActions);

    const total = list.length;
    const paginatedList = list.slice((page - 1) * pageSize, page * pageSize);

    return {
      list: paginatedList,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 获取员工动作时间线
   */
  async getEmployeeActionTimeline(query: {
    employeeId: number;
    startDate?: string;
    endDate?: string;
    orderId?: number;
  }) {
    const { employeeId, startDate, endDate, orderId } = query;

    const whereConditions: any = {
      employeeId,
    };

    if (startDate || endDate) {
      whereConditions.createdAt = {};
      if (startDate) {
        whereConditions.createdAt[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.createdAt[Op.lte] = new Date(endDate + ' 23:59:59');
      }
    }

    if (orderId) {
      whereConditions.orderId = orderId;
    }

    const logs = await ServiceChangeLog.findAll({
      where: whereConditions,
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'serviceTime', 'status'],
        },
      ],
      order: [['createdAt', 'ASC']],
    });

    return logs.map(log => ({
      id: log.id,
      orderId: log.orderId,
      orderSn: log.order?.sn,
      changeType: log.changeType,
      changeTypeLabel: this.getChangeTypeLabel(log.changeType),
      description: log.description,
      actionTime: log.createdAt,
    }));
  }

  /**
   * 获取动作类型标签
   */
  private getChangeTypeLabel(changeType: string): string {
    const labels: { [key: string]: string } = {
      [OrderStatusChangeType.接单]: '接单',
      [OrderStatusChangeType.派单]: '派单',
      [OrderStatusChangeType.转单]: '转单',
      [OrderStatusChangeType.修改服务时间]: '修改服务时间',
      [OrderStatusChangeType.出发]: '出发',
      [OrderStatusChangeType.开始服务]: '开始服务',
      [OrderStatusChangeType.完成订单]: '完成订单',
      [OrderStatusChangeType.取消订单]: '取消订单',
      [OrderStatusChangeType.申请退款]: '申请退款',
      [OrderStatusChangeType.退款]: '退款',
    };

    return labels[changeType] || changeType;
  }
}
