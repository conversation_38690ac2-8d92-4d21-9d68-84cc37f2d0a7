import { Controller, Post, Get, Param, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ServiceDurationCalculatorService } from '../service/service-duration-calculator.service';

@Controller('/service-duration-calculator')
export class ServiceDurationCalculatorController {
  @Inject()
  ctx: Context;

  @Inject()
  serviceDurationCalculatorService: ServiceDurationCalculatorService;

  /**
   * 手动更新所有服务的平均时长
   */
  @Post('/update-all')
  async updateAllServiceAvgDuration() {
    return await this.serviceDurationCalculatorService.updateAllServiceAvgDuration();
  }

  /**
   * 手动更新单个服务的平均时长
   */
  @Post('/update/:serviceId')
  async updateSingleServiceAvgDuration(@Param('serviceId') serviceId: number) {
    return await this.serviceDurationCalculatorService.updateSingleServiceAvgDuration(
      Number(serviceId)
    );
  }

  /**
   * 获取单个服务的平均时长计算结果（不更新数据库）
   */
  @Get('/calculate/:serviceId')
  async calculateServiceAvgDuration(@Param('serviceId') serviceId: number) {
    const avgDuration = await this.serviceDurationCalculatorService.calculateServiceAvgDuration(
      Number(serviceId)
    );
    
    return {
      serviceId: Number(serviceId),
      avgDuration,
      message: avgDuration !== null ? '计算成功' : '没有足够的数据',
    };
  }
}
